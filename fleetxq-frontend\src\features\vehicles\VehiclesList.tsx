import { useEffect, useState } from 'react';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  EllipsisVerticalIcon,
  PlusIcon,
  MapIcon,
  ListBulletIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import {
  fetchVehicles,
  selectVehicles,
  selectVehiclesLoading,
  selectVehiclesError,
  selectVehiclesPagination,
  selectVehiclesFilters,
  selectSelectedVehicles,
  selectVehiclesBulkOperating,
  selectVehiclesBulkOperationError,
  setFilters,
  setPagination,
  setSelectedVehicles,
  toggleVehicleSelection,
  clearSelectedVehicles,
  performBulkOperation,
  clearError
} from './vehiclesSlice';
import type { Vehicle, VehicleSearchFilters, VehicleStatus } from '../../types';
import VehicleCard from './components/VehicleCard';
import VehicleForm from './components/VehicleForm';
import VehicleMap from './components/VehicleMap';
import LoadingSpinner from '../../components/LoadingSpinner';

const VehiclesList = () => {
  const dispatch = useAppDispatch();

  // Select data from Redux store
  const vehicles = useAppSelector(selectVehicles);
  const isLoading = useAppSelector(selectVehiclesLoading);
  const error = useAppSelector(selectVehiclesError);
  const pagination = useAppSelector(selectVehiclesPagination);
  const filters = useAppSelector(selectVehiclesFilters);
  const selectedVehicles = useAppSelector(selectSelectedVehicles);
  const isBulkOperating = useAppSelector(selectVehiclesBulkOperating);
  const bulkOperationError = useAppSelector(selectVehiclesBulkOperationError);

  // Local state
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'map'>('list');
  const [showFilters, setShowFilters] = useState(false);
  const [showVehicleForm, setShowVehicleForm] = useState(false);
  const [editingVehicle, setEditingVehicle] = useState<Vehicle | null>(null);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [sortBy, setSortBy] = useState<'vehicleName' | 'licensePlate' | 'status' | 'lastUpdate' | 'mileage'>('vehicleName');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Fetch vehicles on component mount and when filters/pagination change
  useEffect(() => {
    const searchFilters: VehicleSearchFilters = {
      ...filters,
      sortBy,
      sortOrder
    };

    dispatch(
      fetchVehicles({
        page: pagination.page,
        pageSize: pagination.pageSize,
        ...searchFilters,
      })
    );
  }, [dispatch, pagination.page, pagination.pageSize, filters, sortBy, sortOrder]);

  // Clear errors when component mounts
  useEffect(() => {
    dispatch(clearError());
  }, [dispatch]);

  const handleFiltersChange = (newFilters: Partial<VehicleSearchFilters>) => {
    dispatch(setFilters(newFilters));
    dispatch(setPagination({ page: 1 })); // Reset to first page when filtering
  };

  const handleSearchChange = (search: string) => {
    handleFiltersChange({ search: search || undefined });
  };

  const handlePageChange = (page: number) => {
    dispatch(setPagination({ page }));
  };

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleSelectAll = () => {
    if (selectedVehicles.length === vehicles.length) {
      dispatch(clearSelectedVehicles());
    } else {
      dispatch(setSelectedVehicles(vehicles.map(v => v.id)));
    }
  };

  const handleVehicleSelect = (vehicleId: string) => {
    dispatch(toggleVehicleSelection(vehicleId));
  };

  const handleBulkStatusUpdate = async (status: VehicleStatus) => {
    if (selectedVehicles.length === 0) return;

    try {
      await dispatch(performBulkOperation({
        vehicleIds: selectedVehicles,
        operation: 'updateStatus',
        data: { status }
      })).unwrap();
      setShowBulkActions(false);
    } catch (error) {
      console.error('Bulk operation failed:', error);
    }
  };

  const handleAddVehicle = () => {
    setEditingVehicle(null);
    setShowVehicleForm(true);
  };

  const handleEditVehicle = (vehicle: Vehicle) => {
    setEditingVehicle(vehicle);
    setShowVehicleForm(true);
  };

  const handleFormSuccess = () => {
    setShowVehicleForm(false);
    setEditingVehicle(null);
    // Refresh the list
    dispatch(fetchVehicles({
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...filters,
      sortBy,
      sortOrder
    }));
  };

  const getStatusBadgeClass = (status: Vehicle['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800';
      case 'offline':
        return 'bg-red-100 text-red-800';
      case 'retired':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getSortIcon = (field: typeof sortBy) => {
    if (sortBy !== field) return null;
    return sortOrder === 'asc' ?
      <ArrowUpIcon className="h-4 w-4" /> :
      <ArrowDownIcon className="h-4 w-4" />;
  };

  if (error) {
    return (
      <div className='rounded border border-red-200 bg-red-50 px-4 py-3 text-red-700'>
        Error loading vehicles: {error}
        <button
          onClick={() => dispatch(fetchVehicles())}
          className='ml-2 text-red-600 underline hover:text-red-800'
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Vehicles</h1>
          <p className='text-gray-600 mt-1'>
            Manage your fleet vehicles and monitor their status
          </p>
        </div>
        <div className='flex items-center space-x-3'>
          {/* View Mode Toggle */}
          <div className='flex items-center bg-gray-100 rounded-lg p-1'>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md ${viewMode === 'list' ? 'bg-white shadow-sm' : ''}`}
              title="List view"
            >
              <ListBulletIcon className='h-4 w-4' />
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md ${viewMode === 'grid' ? 'bg-white shadow-sm' : ''}`}
              title="Grid view"
            >
              <div className='h-4 w-4 grid grid-cols-2 gap-0.5'>
                <div className='bg-current rounded-sm'></div>
                <div className='bg-current rounded-sm'></div>
                <div className='bg-current rounded-sm'></div>
                <div className='bg-current rounded-sm'></div>
              </div>
            </button>
            <button
              onClick={() => setViewMode('map')}
              className={`p-2 rounded-md ${viewMode === 'map' ? 'bg-white shadow-sm' : ''}`}
              title="Map view"
            >
              <MapIcon className='h-4 w-4' />
            </button>
          </div>

          <button
            onClick={handleAddVehicle}
            className='btn-primary flex items-center space-x-2'
          >
            <PlusIcon className='h-4 w-4' />
            <span>Add Vehicle</span>
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className='bg-white rounded-lg shadow p-4'>
        <div className='flex items-center justify-between mb-4'>
          <div className='flex items-center space-x-4 flex-1'>
            {/* Search */}
            <div className='relative flex-1 max-w-md'>
              <MagnifyingGlassIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
              <input
                type='text'
                placeholder='Search vehicles, license plates, or VIN...'
                className='input-field pl-10'
                value={filters.search || ''}
                onChange={e => handleSearchChange(e.target.value)}
              />
            </div>

            {/* Quick Filters */}
            <div className='flex items-center space-x-2'>
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg border ${
                  showFilters ? 'bg-blue-50 border-blue-200 text-blue-700' : 'bg-white border-gray-300 text-gray-700'
                } hover:bg-blue-50`}
              >
                <FunnelIcon className='h-4 w-4' />
                <span>Filters</span>
              </button>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedVehicles.length > 0 && (
            <div className='flex items-center space-x-2'>
              <span className='text-sm text-gray-600'>
                {selectedVehicles.length} selected
              </span>
              <div className='relative'>
                <button
                  onClick={() => setShowBulkActions(!showBulkActions)}
                  className='btn-secondary flex items-center space-x-2'
                  disabled={isBulkOperating}
                >
                  {isBulkOperating ? (
                    <LoadingSpinner size='sm' />
                  ) : (
                    <EllipsisVerticalIcon className='h-4 w-4' />
                  )}
                  <span>Actions</span>
                </button>

                {showBulkActions && (
                  <div className='absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10'>
                    <div className='py-1'>
                      <button
                        onClick={() => handleBulkStatusUpdate('active')}
                        className='block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100'
                      >
                        Mark as Active
                      </button>
                      <button
                        onClick={() => handleBulkStatusUpdate('maintenance')}
                        className='block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100'
                      >
                        Mark as Maintenance
                      </button>
                      <button
                        onClick={() => handleBulkStatusUpdate('offline')}
                        className='block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100'
                      >
                        Mark as Offline
                      </button>
                      <hr className='my-1' />
                      <button
                        onClick={() => dispatch(clearSelectedVehicles())}
                        className='block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100'
                      >
                        Clear Selection
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className='border-t border-gray-200 pt-4'>
            <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-1'>
                  Status
                </label>
                <select
                  className='input-field'
                  value={filters.status || ''}
                  onChange={e => handleFiltersChange({ status: (e.target.value as any) || undefined })}
                >
                  <option value=''>All Statuses</option>
                  <option value='active'>Active</option>
                  <option value='maintenance'>Maintenance</option>
                  <option value='offline'>Offline</option>
                  <option value='retired'>Retired</option>
                </select>
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-1'>
                  Vehicle Type
                </label>
                <select
                  className='input-field'
                  value={filters.vehicleType || ''}
                  onChange={e => handleFiltersChange({ vehicleType: e.target.value || undefined })}
                >
                  <option value=''>All Types</option>
                  <option value='Car'>Car</option>
                  <option value='Truck'>Truck</option>
                  <option value='Van'>Van</option>
                  <option value='Bus'>Bus</option>
                  <option value='Motorcycle'>Motorcycle</option>
                </select>
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-1'>
                  Fuel Type
                </label>
                <select
                  className='input-field'
                  value={filters.fuelType || ''}
                  onChange={e => handleFiltersChange({ fuelType: e.target.value || undefined })}
                >
                  <option value=''>All Fuel Types</option>
                  <option value='Gasoline'>Gasoline</option>
                  <option value='Diesel'>Diesel</option>
                  <option value='Electric'>Electric</option>
                  <option value='Hybrid'>Hybrid</option>
                </select>
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-1'>
                  Assignment
                </label>
                <select
                  className='input-field'
                  value={filters.driverId ? 'assigned' : filters.driverId === '' ? 'unassigned' : ''}
                  onChange={e => {
                    if (e.target.value === 'assigned') {
                      handleFiltersChange({ driverId: 'any' });
                    } else if (e.target.value === 'unassigned') {
                      handleFiltersChange({ driverId: '' });
                    } else {
                      handleFiltersChange({ driverId: undefined });
                    }
                  }}
                >
                  <option value=''>All Vehicles</option>
                  <option value='assigned'>Assigned</option>
                  <option value='unassigned'>Unassigned</option>
                </select>
              </div>
            </div>

            <div className='flex items-center justify-between mt-4'>
              <div className='flex items-center space-x-4'>
                <label className='flex items-center space-x-2'>
                  <input
                    type='checkbox'
                    checked={filters.needsMaintenance || false}
                    onChange={e => handleFiltersChange({ needsMaintenance: e.target.checked || undefined })}
                    className='rounded border-gray-300 text-blue-600 focus:ring-blue-500'
                  />
                  <span className='text-sm text-gray-700'>Needs Maintenance</span>
                </label>
                <label className='flex items-center space-x-2'>
                  <input
                    type='checkbox'
                    checked={filters.isAvailable || false}
                    onChange={e => handleFiltersChange({ isAvailable: e.target.checked || undefined })}
                    className='rounded border-gray-300 text-blue-600 focus:ring-blue-500'
                  />
                  <span className='text-sm text-gray-700'>Available Only</span>
                </label>
              </div>

              <button
                onClick={() => {
                  handleFiltersChange({});
                  setShowFilters(false);
                }}
                className='text-sm text-gray-600 hover:text-gray-800'
              >
                Clear All Filters
              </button>
            </div>
          </div>
        )}

        {/* Bulk Operation Error */}
        {bulkOperationError && (
          <div className='bg-red-50 border border-red-200 rounded-lg p-3 mt-4'>
            <p className='text-sm text-red-700'>{bulkOperationError}</p>
          </div>
        )}
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className='flex justify-center py-8'>
          <LoadingSpinner size='lg' />
        </div>
      )}

      {/* Content based on view mode */}
      {!isLoading && (
        <>
          {viewMode === 'map' && (
            <VehicleMap
              selectedVehicleId={selectedVehicles[0]}
              onVehicleSelect={handleVehicleSelect}
              height="600px"
            />
          )}

          {viewMode === 'grid' && (
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
              {vehicles.map(vehicle => (
                <VehicleCard
                  key={vehicle.id}
                  vehicle={vehicle}
                  isSelected={selectedVehicles.includes(vehicle.id)}
                  showSelection={true}
                  onSelect={handleVehicleSelect}
                  onViewDetails={(id) => console.log('View details:', id)}
                  onAssignDriver={(id) => console.log('Assign driver:', id)}
                />
              ))}
            </div>
          )}

          {viewMode === 'list' && (
            <div className='overflow-hidden rounded-lg bg-white shadow'>
              <table className='min-w-full divide-y divide-gray-200'>
                <thead className='bg-gray-50'>
                  <tr>
                    <th className='px-6 py-3 text-left'>
                      <input
                        type='checkbox'
                        checked={selectedVehicles.length === vehicles.length && vehicles.length > 0}
                        onChange={handleSelectAll}
                        className='rounded border-gray-300 text-blue-600 focus:ring-blue-500'
                      />
                    </th>
                    <th
                      className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase cursor-pointer hover:bg-gray-100'
                      onClick={() => handleSort('vehicleName')}
                    >
                      <div className='flex items-center space-x-1'>
                        <span>Vehicle</span>
                        {getSortIcon('vehicleName')}
                      </div>
                    </th>
                    <th
                      className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase cursor-pointer hover:bg-gray-100'
                      onClick={() => handleSort('licensePlate')}
                    >
                      <div className='flex items-center space-x-1'>
                        <span>License Plate</span>
                        {getSortIcon('licensePlate')}
                      </div>
                    </th>
                    <th
                      className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase cursor-pointer hover:bg-gray-100'
                      onClick={() => handleSort('status')}
                    >
                      <div className='flex items-center space-x-1'>
                        <span>Status</span>
                        {getSortIcon('status')}
                      </div>
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase'>
                      Driver
                    </th>
                    <th
                      className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase cursor-pointer hover:bg-gray-100'
                      onClick={() => handleSort('lastUpdate')}
                    >
                      <div className='flex items-center space-x-1'>
                        <span>Last Update</span>
                        {getSortIcon('lastUpdate')}
                      </div>
                    </th>
                    <th className='px-6 py-3 text-right text-xs font-medium tracking-wider text-gray-500 uppercase'>
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className='divide-y divide-gray-200 bg-white'>
                  {vehicles.map(vehicle => (
                    <tr
                      key={vehicle.id}
                      className={`hover:bg-gray-50 ${selectedVehicles.includes(vehicle.id) ? 'bg-blue-50' : ''}`}
                    >
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <input
                          type='checkbox'
                          checked={selectedVehicles.includes(vehicle.id)}
                          onChange={() => handleVehicleSelect(vehicle.id)}
                          className='rounded border-gray-300 text-blue-600 focus:ring-blue-500'
                        />
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div>
                          <div className='text-sm font-medium text-gray-900'>
                            {vehicle.vehicleName || `${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                          </div>
                          <div className='text-sm text-gray-500'>
                            {vehicle.vehicleType} • VIN: {vehicle.vin}
                          </div>
                        </div>
                      </td>
                      <td className='px-6 py-4 text-sm whitespace-nowrap text-gray-900'>
                        {vehicle.licensePlate}
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <span
                          className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getStatusBadgeClass(vehicle.status)}`}
                        >
                          {vehicle.status}
                        </span>
                        {vehicle.needsMaintenance && (
                          <span className='ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800'>
                            Maintenance Due
                          </span>
                        )}
                      </td>
                      <td className='px-6 py-4 text-sm whitespace-nowrap text-gray-900'>
                        {vehicle.driverId ? 'Assigned' : 'Unassigned'}
                      </td>
                      <td className='px-6 py-4 text-sm whitespace-nowrap text-gray-500'>
                        {vehicle.lastTelemetryUpdate ?
                          new Date(vehicle.lastTelemetryUpdate).toLocaleDateString() :
                          'Never'
                        }
                      </td>
                      <td className='px-6 py-4 text-right text-sm font-medium whitespace-nowrap'>
                        <button
                          onClick={() => handleEditVehicle(vehicle)}
                          className='mr-3 text-blue-600 hover:text-blue-900'
                        >
                          Edit
                        </button>
                        <button className='text-red-600 hover:text-red-900'>
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* Empty State */}
              {vehicles.length === 0 && (
                <div className='py-12 text-center'>
                  <div className='mx-auto h-12 w-12 text-gray-400'>
                    <svg fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                      <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={1} d='M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z' />
                      <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={1} d='M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l2.414 2.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0M15 17a2 2 0 104 0' />
                    </svg>
                  </div>
                  <h3 className='mt-2 text-sm font-medium text-gray-900'>No vehicles found</h3>
                  <p className='mt-1 text-sm text-gray-500'>
                    {Object.keys(filters).length > 0 ?
                      'Try adjusting your search or filters.' :
                      'Get started by adding your first vehicle.'
                    }
                  </p>
                  {Object.keys(filters).length === 0 && (
                    <div className='mt-6'>
                      <button
                        onClick={handleAddVehicle}
                        className='btn-primary'
                      >
                        Add Vehicle
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </>
      )}

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className='bg-white rounded-lg shadow p-4'>
          <div className='flex items-center justify-between'>
            <div className='text-sm text-gray-700'>
              Showing {(pagination.page - 1) * pagination.pageSize + 1} to{' '}
              {Math.min(pagination.page * pagination.pageSize, pagination.total)}{' '}
              of {pagination.total} results
            </div>
            <div className='flex items-center space-x-2'>
              <button
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page <= 1}
                className='px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50'
              >
                Previous
              </button>

              {/* Page numbers */}
              <div className='flex space-x-1'>
                {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                  let pageNum;
                  if (pagination.totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (pagination.page <= 3) {
                    pageNum = i + 1;
                  } else if (pagination.page >= pagination.totalPages - 2) {
                    pageNum = pagination.totalPages - 4 + i;
                  } else {
                    pageNum = pagination.page - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`px-3 py-2 text-sm font-medium rounded-md ${
                        pagination.page === pageNum
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>

              <button
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page >= pagination.totalPages}
                className='px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50'
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Vehicle Form Modal */}
      <VehicleForm
        vehicle={editingVehicle}
        isOpen={showVehicleForm}
        onClose={() => {
          setShowVehicleForm(false);
          setEditingVehicle(null);
        }}
        onSuccess={handleFormSuccess}
      />
    </div>
  );
};

export default VehiclesList;
