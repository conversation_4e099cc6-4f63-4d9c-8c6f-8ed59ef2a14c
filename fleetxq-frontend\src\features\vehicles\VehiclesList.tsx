import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import {
  fetchVehicles,
  selectVehicles,
  selectVehiclesLoading,
  selectVehiclesError,
  selectVehiclesPagination,
  selectVehiclesFilters,
  setFilters,
  setPagination,
} from './vehiclesSlice';
import type { Vehicle } from '../../types';

const VehiclesList = () => {
  const dispatch = useAppDispatch();

  // Select data from Redux store
  const vehicles = useAppSelector(selectVehicles);
  const isLoading = useAppSelector(selectVehiclesLoading);
  const error = useAppSelector(selectVehiclesError);
  const pagination = useAppSelector(selectVehiclesPagination);
  const filters = useAppSelector(selectVehiclesFilters);

  // Fetch vehicles on component mount and when filters/pagination change
  useEffect(() => {
    dispatch(
      fetchVehicles({
        page: pagination.page,
        pageSize: pagination.pageSize,
        ...filters,
      })
    );
  }, [dispatch, pagination.page, pagination.pageSize, filters]);

  const handleStatusFilter = (
    status: 'active' | 'maintenance' | 'inactive' | undefined
  ) => {
    dispatch(setFilters({ status }));
    dispatch(setPagination({ page: 1 })); // Reset to first page when filtering
  };

  const handleSearchFilter = (search: string) => {
    dispatch(setFilters({ search: search || undefined }));
    dispatch(setPagination({ page: 1 })); // Reset to first page when searching
  };

  const handlePageChange = (page: number) => {
    dispatch(setPagination({ page }));
  };

  const getStatusBadgeClass = (status: Vehicle['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (error) {
    return (
      <div className='rounded border border-red-200 bg-red-50 px-4 py-3 text-red-700'>
        Error loading vehicles: {error}
        <button
          onClick={() => dispatch(fetchVehicles())}
          className='ml-2 text-red-600 underline hover:text-red-800'
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <h1 className='text-2xl font-bold text-gray-900'>Vehicles</h1>
        <button className='btn-primary'>Add Vehicle</button>
      </div>

      {/* Filters */}
      <div className='space-y-4 rounded-lg bg-white p-4 shadow'>
        <div className='flex flex-wrap gap-4'>
          <div>
            <label className='mb-1 block text-sm font-medium text-gray-700'>
              Search
            </label>
            <input
              type='text'
              placeholder='Search vehicles...'
              className='input-field'
              value={filters.search || ''}
              onChange={e => handleSearchFilter(e.target.value)}
            />
          </div>

          <div>
            <label className='mb-1 block text-sm font-medium text-gray-700'>
              Status
            </label>
            <select
              className='input-field'
              value={filters.status || ''}
              onChange={e =>
                handleStatusFilter((e.target.value as any) || undefined)
              }
            >
              <option value=''>All Statuses</option>
              <option value='active'>Active</option>
              <option value='maintenance'>Maintenance</option>
              <option value='inactive'>Inactive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className='flex justify-center py-8'>
          <div className='h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600'></div>
        </div>
      )}

      {/* Vehicles List */}
      {!isLoading && (
        <div className='overflow-hidden rounded-lg bg-white shadow'>
          <table className='min-w-full divide-y divide-gray-200'>
            <thead className='bg-gray-50'>
              <tr>
                <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase'>
                  Vehicle
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase'>
                  License Plate
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase'>
                  Status
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase'>
                  Driver
                </th>
                <th className='px-6 py-3 text-right text-xs font-medium tracking-wider text-gray-500 uppercase'>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className='divide-y divide-gray-200 bg-white'>
              {vehicles.map(vehicle => (
                <tr key={vehicle.id} className='hover:bg-gray-50'>
                  <td className='px-6 py-4 whitespace-nowrap'>
                    <div>
                      <div className='text-sm font-medium text-gray-900'>
                        {vehicle.year} {vehicle.make} {vehicle.model}
                      </div>
                      <div className='text-sm text-gray-500'>
                        VIN: {vehicle.vin}
                      </div>
                    </div>
                  </td>
                  <td className='px-6 py-4 text-sm whitespace-nowrap text-gray-900'>
                    {vehicle.licensePlate}
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap'>
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getStatusBadgeClass(vehicle.status)}`}
                    >
                      {vehicle.status}
                    </span>
                  </td>
                  <td className='px-6 py-4 text-sm whitespace-nowrap text-gray-900'>
                    {vehicle.driverId ? 'Assigned' : 'Unassigned'}
                  </td>
                  <td className='px-6 py-4 text-right text-sm font-medium whitespace-nowrap'>
                    <button className='mr-3 text-blue-600 hover:text-blue-900'>
                      Edit
                    </button>
                    <button className='text-red-600 hover:text-red-900'>
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Empty State */}
          {vehicles.length === 0 && !isLoading && (
            <div className='py-8 text-center'>
              <p className='text-gray-500'>No vehicles found</p>
            </div>
          )}
        </div>
      )}

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className='flex items-center justify-between'>
          <div className='text-sm text-gray-700'>
            Showing {(pagination.page - 1) * pagination.pageSize + 1} to{' '}
            {Math.min(pagination.page * pagination.pageSize, pagination.total)}{' '}
            of {pagination.total} results
          </div>
          <div className='flex space-x-2'>
            <button
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
              className='rounded border px-3 py-1 disabled:cursor-not-allowed disabled:opacity-50'
            >
              Previous
            </button>
            <span className='px-3 py-1'>
              Page {pagination.page} of {pagination.totalPages}
            </span>
            <button
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.totalPages}
              className='rounded border px-3 py-1 disabled:cursor-not-allowed disabled:opacity-50'
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default VehiclesList;
