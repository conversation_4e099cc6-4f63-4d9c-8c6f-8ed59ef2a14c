import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import {
  PencilIcon,
  MapPinIcon,
  FuelIcon,
  CogIcon,
  ClockIcon,
  UserIcon,
  TruckIcon,
  WrenchScrewdriverIcon,
  ChartBarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import VehicleNavigation from './VehicleNavigation';
import VehicleAnalyticsDashboard from './VehicleAnalyticsDashboard';
import VehicleMap from './VehicleMap';
import VehicleAssignmentHistory from './VehicleAssignmentHistory';
import VehicleForm from './VehicleForm';
import { useAppDispatch, useAppSelector } from '../../../store/hooks';
import {
  fetchVehicleById,
  fetchVehicleAnalytics,
  fetchAssignmentHistory,
  fetchMaintenanceHistory,
  selectCurrentVehicle,
  selectVehiclesLoading,
  selectVehicleAnalyticsById,
  selectAssignmentHistory,
  selectMaintenanceHistory,
  selectVehiclesLoadingAnalytics
} from '../vehiclesSlice';
import LoadingSpinner from '../../../components/LoadingSpinner';
import type { Vehicle, VehicleAnalytics } from '../../../types';

interface VehicleDetailsProps {
  vehicleId?: string;
  onEdit?: (vehicle: Vehicle) => void;
  onAssignDriver?: (vehicleId: string) => void;
  onScheduleMaintenance?: (vehicleId: string) => void;
}

const VehicleDetails: React.FC<VehicleDetailsProps> = ({
  vehicleId: propVehicleId,
  onEdit,
  onAssignDriver,
  onScheduleMaintenance
}) => {
  const { vehicleId: paramVehicleId } = useParams<{ vehicleId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();

  const vehicleId = propVehicleId || paramVehicleId;
  const [showEditForm, setShowEditForm] = useState(false);

  // Determine current section from URL
  const getCurrentSection = () => {
    const path = location.pathname;
    if (path.includes('/analytics')) return 'analytics';
    if (path.includes('/location')) return 'location';
    if (path.includes('/maintenance')) return 'maintenance';
    if (path.includes('/assignments')) return 'assignments';
    return 'overview';
  };

  const currentSection = getCurrentSection();

  const vehicle = useAppSelector(selectCurrentVehicle);
  const isLoading = useAppSelector(selectVehiclesLoading);
  const isLoadingAnalytics = useAppSelector(selectVehiclesLoadingAnalytics);
  const analytics = useAppSelector(selectVehicleAnalyticsById(vehicleId || ''));
  const assignmentHistory = useAppSelector(selectAssignmentHistory);
  const maintenanceHistory = useAppSelector(selectMaintenanceHistory);

  useEffect(() => {
    if (vehicleId) {
      dispatch(fetchVehicleById(vehicleId));
      dispatch(fetchVehicleAnalytics(vehicleId));
      dispatch(fetchAssignmentHistory(vehicleId));
      dispatch(fetchMaintenanceHistory(vehicleId));
    }
  }, [dispatch, vehicleId]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const handleEdit = () => {
    if (onEdit && vehicle) {
      onEdit(vehicle);
    } else {
      setShowEditForm(true);
    }
  };

  const handleFormSuccess = () => {
    setShowEditForm(false);
    // Refresh vehicle data
    if (vehicleId) {
      dispatch(fetchVehicleById(vehicleId));
    }
  };

  if (!vehicle) {
    return (
      <div className="min-h-screen bg-gray-50">
        <VehicleNavigation />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-8">
            <p className="text-gray-500">Vehicle not found</p>
          </div>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: Vehicle['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800';
      case 'offline':
        return 'bg-red-100 text-red-800';
      case 'retired':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <VehicleNavigation
        vehicleId={vehicleId}
        vehicleName={vehicle.vehicleName || `${vehicle.year} ${vehicle.make} ${vehicle.model}`}
      />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Vehicle Header */}
        <div className="bg-white rounded-lg shadow mb-6 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <TruckIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {vehicle.vehicleName || `${vehicle.year} ${vehicle.make} ${vehicle.model}`}
                </h1>
                <p className="text-gray-600">{vehicle.licensePlate} • VIN: {vehicle.vin}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(vehicle.status)}`}>
                {vehicle.status}
              </span>
              <button
                onClick={handleEdit}
                className="btn-secondary flex items-center space-x-2"
              >
                <PencilIcon className="h-4 w-4" />
                <span>Edit</span>
              </button>
            </div>
          </div>
        </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <MapPinIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Current Location</p>
              <p className="font-semibold text-gray-900">
                {vehicle.currentLocation?.address || 'Unknown'}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <FuelIcon className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Fuel Level</p>
              <p className="font-semibold text-gray-900">
                {vehicle.currentFuelLevel ? `${Math.round(vehicle.currentFuelLevel)}%` : 'N/A'}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <CogIcon className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Engine Status</p>
              <p className="font-semibold text-gray-900 capitalize">
                {vehicle.engineStatus || 'Unknown'}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-orange-100 rounded-lg">
              <ClockIcon className="h-6 w-6 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Last Update</p>
              <p className="font-semibold text-gray-900">
                {formatDate(vehicle.lastTelemetryUpdate)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {vehicle.requiresImmediateAttention && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
            <h3 className="text-sm font-medium text-red-800">Immediate Attention Required</h3>
          </div>
          <p className="mt-1 text-sm text-red-700">
            This vehicle requires immediate attention due to critical alerts or maintenance issues.
          </p>
        </div>
      )}

        {/* Alerts */}
        {vehicle.requiresImmediateAttention && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-2">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
              <h3 className="text-sm font-medium text-red-800">Immediate Attention Required</h3>
            </div>
            <p className="mt-1 text-sm text-red-700">
              This vehicle requires immediate attention due to critical alerts or maintenance issues.
            </p>
          </div>
        )}

        {/* Section Content */}
        {currentSection === 'overview' && (
          <VehicleOverviewTab
            vehicle={vehicle}
            onAssignDriver={onAssignDriver}
            onScheduleMaintenance={onScheduleMaintenance}
          />
        )}

        {currentSection === 'location' && (
          <VehicleMap
            selectedVehicleId={vehicleId}
            height="600px"
            className="bg-white rounded-lg shadow"
          />
        )}

        {currentSection === 'analytics' && (
          <VehicleAnalyticsDashboard vehicle={vehicle} />
        )}

        {currentSection === 'maintenance' && (
          <VehicleMaintenanceTab
            vehicle={vehicle}
            maintenanceHistory={maintenanceHistory}
            onScheduleMaintenance={onScheduleMaintenance}
          />
        )}

        {currentSection === 'assignments' && (
          <VehicleAssignmentHistory vehicleId={vehicleId!} />
        )}
      </div>

      {/* Edit Form Modal */}
      <VehicleForm
        vehicle={vehicle}
        isOpen={showEditForm}
        onClose={() => setShowEditForm(false)}
        onSuccess={handleFormSuccess}
      />
    </div>
  );
};

// Overview tab component
const VehicleOverviewTab: React.FC<{
  vehicle: Vehicle;
  onAssignDriver?: (vehicleId: string) => void;
  onScheduleMaintenance?: (vehicleId: string) => void;
}> = ({ vehicle, onAssignDriver, onScheduleMaintenance }) => (
  <div className="space-y-6">
    {/* Quick Stats */}
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <MapPinIcon className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <p className="text-sm text-gray-500">Current Location</p>
            <p className="font-semibold text-gray-900">
              {vehicle.currentLocation?.address || 'Unknown'}
            </p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <FuelIcon className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <p className="text-sm text-gray-500">Fuel Level</p>
            <p className="font-semibold text-gray-900">
              {vehicle.currentFuelLevel ? `${Math.round(vehicle.currentFuelLevel)}%` : 'N/A'}
            </p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-purple-100 rounded-lg">
            <CogIcon className="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <p className="text-sm text-gray-500">Engine Status</p>
            <p className="font-semibold text-gray-900 capitalize">
              {vehicle.engineStatus || 'Unknown'}
            </p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-orange-100 rounded-lg">
            <ClockIcon className="h-6 w-6 text-orange-600" />
          </div>
          <div>
            <p className="text-sm text-gray-500">Mileage</p>
            <p className="font-semibold text-gray-900">
              {vehicle.currentMileage.toLocaleString()} mi
            </p>
          </div>
        </div>
      </div>
    </div>

    {/* Vehicle Details */}
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <h3 className="text-lg font-semibold mb-4">Vehicle Information</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div>
          <label className="text-sm font-medium text-gray-500">Make & Model</label>
          <p className="text-gray-900">{vehicle.make} {vehicle.model}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Year</label>
          <p className="text-gray-900">{vehicle.year}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Vehicle Type</label>
          <p className="text-gray-900">{vehicle.vehicleType}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Fuel Type</label>
          <p className="text-gray-900">{vehicle.fuelType}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Color</label>
          <p className="text-gray-900">{vehicle.color || 'Not specified'}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Tank Capacity</label>
          <p className="text-gray-900">{vehicle.fuelTankCapacity ? `${vehicle.fuelTankCapacity}L` : 'Not specified'}</p>
        </div>
      </div>
    </div>

    {/* Actions */}
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
      <div className="flex flex-wrap gap-3">
        {!vehicle.driverId && onAssignDriver && (
          <button
            onClick={() => onAssignDriver(vehicle.id)}
            className="btn-primary"
          >
            Assign Driver
          </button>
        )}
        {onScheduleMaintenance && (
          <button
            onClick={() => onScheduleMaintenance(vehicle.id)}
            className="btn-secondary"
          >
            Schedule Maintenance
          </button>
        )}
      </div>
    </div>
  </div>
);

// Maintenance tab component
const VehicleMaintenanceTab: React.FC<{
  vehicle: Vehicle;
  maintenanceHistory: any[];
  onScheduleMaintenance?: (vehicleId: string) => void;
}> = ({ vehicle, maintenanceHistory, onScheduleMaintenance }) => (
  <div className="space-y-6">
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Maintenance Overview</h3>
        {onScheduleMaintenance && (
          <button
            onClick={() => onScheduleMaintenance(vehicle.id)}
            className="btn-primary"
          >
            Schedule Maintenance
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="text-sm font-medium text-gray-500">Last Maintenance</label>
          <p className="text-gray-900">
            {vehicle.lastMaintenanceDate ?
              new Date(vehicle.lastMaintenanceDate).toLocaleDateString() :
              'No records'
            }
          </p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Next Maintenance</label>
          <p className="text-gray-900">
            {vehicle.nextMaintenanceDate ?
              new Date(vehicle.nextMaintenanceDate).toLocaleDateString() :
              'Not scheduled'
            }
          </p>
        </div>
      </div>

      {vehicle.needsMaintenance && (
        <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-sm text-yellow-800">
            This vehicle is due for maintenance. Please schedule a service appointment.
          </p>
        </div>
      )}
    </div>
  </div>
);

export default VehicleDetails;
