import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeftIcon,
  PencilIcon,
  MapPinIcon,
  FuelIcon,
  CogIcon,
  ClockIcon,
  UserIcon,
  TruckIcon,
  WrenchScrewdriverIcon,
  ChartBarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { useAppDispatch, useAppSelector } from '../../../store/hooks';
import {
  fetchVehicleById,
  fetchVehicleAnalytics,
  fetchAssignmentHistory,
  fetchMaintenanceHistory,
  selectCurrentVehicle,
  selectVehiclesLoading,
  selectVehicleAnalyticsById,
  selectAssignmentHistory,
  selectMaintenanceHistory,
  selectVehiclesLoadingAnalytics
} from '../vehiclesSlice';
import LoadingSpinner from '../../../components/LoadingSpinner';
import type { Vehicle, VehicleAnalytics } from '../../../types';

interface VehicleDetailsProps {
  vehicleId?: string;
  onEdit?: (vehicle: Vehicle) => void;
  onAssignDriver?: (vehicleId: string) => void;
  onScheduleMaintenance?: (vehicleId: string) => void;
}

const VehicleDetails: React.FC<VehicleDetailsProps> = ({
  vehicleId: propVehicleId,
  onEdit,
  onAssignDriver,
  onScheduleMaintenance
}) => {
  const { vehicleId: paramVehicleId } = useParams<{ vehicleId: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const vehicleId = propVehicleId || paramVehicleId;
  const [activeTab, setActiveTab] = useState<'overview' | 'telemetry' | 'maintenance' | 'assignments' | 'analytics'>('overview');

  const vehicle = useAppSelector(selectCurrentVehicle);
  const isLoading = useAppSelector(selectVehiclesLoading);
  const isLoadingAnalytics = useAppSelector(selectVehiclesLoadingAnalytics);
  const analytics = useAppSelector(selectVehicleAnalyticsById(vehicleId || ''));
  const assignmentHistory = useAppSelector(selectAssignmentHistory);
  const maintenanceHistory = useAppSelector(selectMaintenanceHistory);

  useEffect(() => {
    if (vehicleId) {
      dispatch(fetchVehicleById(vehicleId));
      dispatch(fetchVehicleAnalytics(vehicleId));
      dispatch(fetchAssignmentHistory(vehicleId));
      dispatch(fetchMaintenanceHistory(vehicleId));
    }
  }, [dispatch, vehicleId]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!vehicle) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Vehicle not found</p>
        <button
          onClick={() => navigate('/vehicles')}
          className="mt-4 btn-primary"
        >
          Back to Vehicles
        </button>
      </div>
    );
  }

  const getStatusColor = (status: Vehicle['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800';
      case 'offline':
        return 'bg-red-100 text-red-800';
      case 'retired':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: TruckIcon },
    { id: 'telemetry', label: 'Telemetry', icon: MapPinIcon },
    { id: 'maintenance', label: 'Maintenance', icon: WrenchScrewdriverIcon },
    { id: 'assignments', label: 'Assignments', icon: UserIcon },
    { id: 'analytics', label: 'Analytics', icon: ChartBarIcon },
  ] as const;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/vehicles')}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {vehicle.vehicleName || `${vehicle.year} ${vehicle.make} ${vehicle.model}`}
            </h1>
            <p className="text-gray-600">{vehicle.licensePlate} • VIN: {vehicle.vin}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(vehicle.status)}`}>
            {vehicle.status}
          </span>
          <button
            onClick={() => onEdit?.(vehicle)}
            className="btn-secondary flex items-center space-x-2"
          >
            <PencilIcon className="h-4 w-4" />
            <span>Edit</span>
          </button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <MapPinIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Current Location</p>
              <p className="font-semibold text-gray-900">
                {vehicle.currentLocation?.address || 'Unknown'}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <FuelIcon className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Fuel Level</p>
              <p className="font-semibold text-gray-900">
                {vehicle.currentFuelLevel ? `${Math.round(vehicle.currentFuelLevel)}%` : 'N/A'}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <CogIcon className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Engine Status</p>
              <p className="font-semibold text-gray-900 capitalize">
                {vehicle.engineStatus || 'Unknown'}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-orange-100 rounded-lg">
              <ClockIcon className="h-6 w-6 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Last Update</p>
              <p className="font-semibold text-gray-900">
                {formatDate(vehicle.lastTelemetryUpdate)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {vehicle.requiresImmediateAttention && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
            <h3 className="text-sm font-medium text-red-800">Immediate Attention Required</h3>
          </div>
          <p className="mt-1 text-sm text-red-700">
            This vehicle requires immediate attention due to critical alerts or maintenance issues.
          </p>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'overview' && (
          <VehicleOverviewTab 
            vehicle={vehicle} 
            onAssignDriver={onAssignDriver}
            onScheduleMaintenance={onScheduleMaintenance}
          />
        )}
        {activeTab === 'telemetry' && (
          <VehicleTelemetryTab vehicle={vehicle} />
        )}
        {activeTab === 'maintenance' && (
          <VehicleMaintenanceTab 
            vehicle={vehicle} 
            maintenanceHistory={maintenanceHistory}
            onScheduleMaintenance={onScheduleMaintenance}
          />
        )}
        {activeTab === 'assignments' && (
          <VehicleAssignmentsTab 
            vehicle={vehicle} 
            assignmentHistory={assignmentHistory}
            onAssignDriver={onAssignDriver}
          />
        )}
        {activeTab === 'analytics' && (
          <VehicleAnalyticsTab 
            vehicle={vehicle} 
            analytics={analytics}
            isLoading={isLoadingAnalytics}
          />
        )}
      </div>
    </div>
  );
};

// Placeholder components for tabs - these will be implemented separately
const VehicleOverviewTab: React.FC<any> = ({ vehicle }) => (
  <div className="card">
    <h3 className="text-lg font-semibold mb-4">Vehicle Information</h3>
    <div className="grid grid-cols-2 gap-4">
      <div>
        <label className="text-sm font-medium text-gray-500">Vehicle Name</label>
        <p className="text-gray-900">{vehicle.vehicleName}</p>
      </div>
      <div>
        <label className="text-sm font-medium text-gray-500">Vehicle Type</label>
        <p className="text-gray-900">{vehicle.vehicleType}</p>
      </div>
      {/* Add more fields as needed */}
    </div>
  </div>
);

const VehicleTelemetryTab: React.FC<any> = () => (
  <div className="card">
    <p className="text-gray-500">Telemetry data will be displayed here</p>
  </div>
);

const VehicleMaintenanceTab: React.FC<any> = () => (
  <div className="card">
    <p className="text-gray-500">Maintenance history will be displayed here</p>
  </div>
);

const VehicleAssignmentsTab: React.FC<any> = () => (
  <div className="card">
    <p className="text-gray-500">Assignment history will be displayed here</p>
  </div>
);

const VehicleAnalyticsTab: React.FC<any> = () => (
  <div className="card">
    <p className="text-gray-500">Analytics will be displayed here</p>
  </div>
);

export default VehicleDetails;
