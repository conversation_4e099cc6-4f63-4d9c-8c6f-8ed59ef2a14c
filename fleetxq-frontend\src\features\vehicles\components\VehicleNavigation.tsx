import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  ArrowLeftIcon,
  TruckIcon,
  MapIcon,
  ChartBarIcon,
  WrenchScrewdriverIcon,
  UserIcon
} from '@heroicons/react/24/outline';

interface VehicleNavigationProps {
  vehicleId?: string;
  vehicleName?: string;
  className?: string;
}

const VehicleNavigation: React.FC<VehicleNavigationProps> = ({
  vehicleId,
  vehicleName,
  className = ''
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const navigationItems = [
    {
      id: 'overview',
      label: 'Overview',
      icon: TruckIcon,
      path: `/vehicles/${vehicleId}`,
      description: 'Vehicle details and status'
    },
    {
      id: 'location',
      label: 'Location',
      icon: MapIcon,
      path: `/vehicles/${vehicleId}/location`,
      description: 'Real-time location and tracking'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: ChartBarIcon,
      path: `/vehicles/${vehicleId}/analytics`,
      description: 'Performance and utilization metrics'
    },
    {
      id: 'maintenance',
      label: 'Maintenance',
      icon: WrenchScrewdriverIcon,
      path: `/vehicles/${vehicleId}/maintenance`,
      description: 'Maintenance history and alerts'
    },
    {
      id: 'assignments',
      label: 'Assignments',
      icon: UserIcon,
      path: `/vehicles/${vehicleId}/assignments`,
      description: 'Driver assignment history'
    }
  ];

  const handleBackToList = () => {
    navigate('/vehicles');
  };

  const handleNavigateToSection = (path: string) => {
    navigate(path);
  };

  const isCurrentPath = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className={`bg-white border-b border-gray-200 ${className}`}>
      {/* Header with back button */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleBackToList}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5" />
            <span className="text-sm font-medium">Back to Vehicles</span>
          </button>
          
          {vehicleName && (
            <>
              <div className="h-4 w-px bg-gray-300" />
              <div className="flex items-center space-x-2">
                <TruckIcon className="h-5 w-5 text-gray-400" />
                <h1 className="text-lg font-semibold text-gray-900">{vehicleName}</h1>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Navigation tabs */}
      {vehicleId && (
        <div className="px-6">
          <nav className="flex space-x-8" aria-label="Vehicle sections">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isCurrent = isCurrentPath(item.path);
              
              return (
                <button
                  key={item.id}
                  onClick={() => handleNavigateToSection(item.path)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    isCurrent
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                  aria-current={isCurrent ? 'page' : undefined}
                  title={item.description}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.label}</span>
                </button>
              );
            })}
          </nav>
        </div>
      )}
    </div>
  );
};

export default VehicleNavigation;
