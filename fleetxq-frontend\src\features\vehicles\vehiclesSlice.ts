import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { Vehicle, PaginatedResponse } from '../../types';
import { apiService } from '../../services/api';

// Vehicle creation/update interface
interface VehicleFormData {
  make: string;
  model: string;
  year: number;
  licensePlate: string;
  vin: string;
  status: 'active' | 'maintenance' | 'inactive';
  driverId?: string;
}

// Vehicles state interface
interface VehiclesState {
  vehicles: Vehicle[];
  currentVehicle: Vehicle | null;
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  filters: {
    status?: 'active' | 'maintenance' | 'inactive';
    search?: string;
  };
}

const initialState: VehiclesState = {
  vehicles: [],
  currentVehicle: null,
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
  pagination: {
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  },
  filters: {},
};

// Async thunks
export const fetchVehicles = createAsyncThunk(
  'vehicles/fetchVehicles',
  async (
    params: {
      page?: number;
      pageSize?: number;
      status?: string;
      search?: string;
    } = {},
    { rejectWithValue }
  ) => {
    try {
      const { page = 1, pageSize = 10, status, search } = params;
      let url = `/vehicles?page=${page}&pageSize=${pageSize}`;

      if (status) url += `&status=${status}`;
      if (search) url += `&search=${encodeURIComponent(search)}`;

      const response = await apiService.get<PaginatedResponse<Vehicle>>(url);
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to fetch vehicles';
      return rejectWithValue(message);
    }
  }
);

export const fetchVehicleById = createAsyncThunk(
  'vehicles/fetchVehicleById',
  async (vehicleId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.get<Vehicle>(`/vehicles/${vehicleId}`);
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to fetch vehicle';
      return rejectWithValue(message);
    }
  }
);

export const createVehicle = createAsyncThunk(
  'vehicles/createVehicle',
  async (vehicleData: VehicleFormData, { rejectWithValue }) => {
    try {
      const response = await apiService.post<Vehicle>('/vehicles', vehicleData);
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to create vehicle';
      return rejectWithValue(message);
    }
  }
);

export const updateVehicle = createAsyncThunk(
  'vehicles/updateVehicle',
  async (
    { id, data }: { id: string; data: Partial<VehicleFormData> },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiService.put<Vehicle>(`/vehicles/${id}`, data);
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to update vehicle';
      return rejectWithValue(message);
    }
  }
);

export const deleteVehicle = createAsyncThunk(
  'vehicles/deleteVehicle',
  async (vehicleId: string, { rejectWithValue }) => {
    try {
      await apiService.delete(`/vehicles/${vehicleId}`);
      return vehicleId;
    } catch (error: any) {
      const message =
        error.response?.data?.message || 'Failed to delete vehicle';
      return rejectWithValue(message);
    }
  }
);

const vehiclesSlice = createSlice({
  name: 'vehicles',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null;
    },
    setCurrentVehicle: (state, action: PayloadAction<Vehicle | null>) => {
      state.currentVehicle = action.payload;
    },
    setFilters: (
      state,
      action: PayloadAction<Partial<VehiclesState['filters']>>
    ) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: state => {
      state.filters = {};
    },
    setPagination: (
      state,
      action: PayloadAction<Partial<VehiclesState['pagination']>>
    ) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
  },
  extraReducers: builder => {
    builder
      // Fetch vehicles cases
      .addCase(fetchVehicles.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchVehicles.fulfilled, (state, action) => {
        state.isLoading = false;
        state.vehicles = action.payload.data;
        state.pagination = {
          page: action.payload.page,
          pageSize: action.payload.pageSize,
          total: action.payload.total,
          totalPages: action.payload.totalPages,
        };
        state.error = null;
      })
      .addCase(fetchVehicles.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch vehicle by ID cases
      .addCase(fetchVehicleById.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchVehicleById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentVehicle = action.payload;
        state.error = null;
      })
      .addCase(fetchVehicleById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Create vehicle cases
      .addCase(createVehicle.pending, state => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createVehicle.fulfilled, (state, action) => {
        state.isCreating = false;
        state.vehicles.unshift(action.payload);
        state.pagination.total += 1;
        state.error = null;
      })
      .addCase(createVehicle.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload as string;
      })
      // Update vehicle cases
      .addCase(updateVehicle.pending, state => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateVehicle.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.vehicles.findIndex(v => v.id === action.payload.id);
        if (index !== -1) {
          state.vehicles[index] = action.payload;
        }
        if (state.currentVehicle?.id === action.payload.id) {
          state.currentVehicle = action.payload;
        }
        state.error = null;
      })
      .addCase(updateVehicle.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      })
      // Delete vehicle cases
      .addCase(deleteVehicle.pending, state => {
        state.isDeleting = true;
        state.error = null;
      })
      .addCase(deleteVehicle.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.vehicles = state.vehicles.filter(v => v.id !== action.payload);
        if (state.currentVehicle?.id === action.payload) {
          state.currentVehicle = null;
        }
        state.pagination.total = Math.max(0, state.pagination.total - 1);
        state.error = null;
      })
      .addCase(deleteVehicle.rejected, (state, action) => {
        state.isDeleting = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setCurrentVehicle,
  setFilters,
  clearFilters,
  setPagination,
} = vehiclesSlice.actions;

// Selectors
export const selectVehicles = (state: { vehicles: VehiclesState }) =>
  state.vehicles.vehicles;
export const selectCurrentVehicle = (state: { vehicles: VehiclesState }) =>
  state.vehicles.currentVehicle;
export const selectVehiclesLoading = (state: { vehicles: VehiclesState }) =>
  state.vehicles.isLoading;
export const selectVehiclesCreating = (state: { vehicles: VehiclesState }) =>
  state.vehicles.isCreating;
export const selectVehiclesUpdating = (state: { vehicles: VehiclesState }) =>
  state.vehicles.isUpdating;
export const selectVehiclesDeleting = (state: { vehicles: VehiclesState }) =>
  state.vehicles.isDeleting;
export const selectVehiclesError = (state: { vehicles: VehiclesState }) =>
  state.vehicles.error;
export const selectVehiclesPagination = (state: { vehicles: VehiclesState }) =>
  state.vehicles.pagination;
export const selectVehiclesFilters = (state: { vehicles: VehiclesState }) =>
  state.vehicles.filters;

export default vehiclesSlice.reducer;
