import { createBrowserRouter, Navigate } from 'react-router-dom';
import { lazy, Suspense } from 'react';
import MainLayout from '../layouts/MainLayout';
import AuthLayout from '../layouts/AuthLayout';
import ProtectedRoute from './ProtectedRoute';
import LoadingSpinner from '../components/LoadingSpinner';

// Lazy load components for code splitting
const LoginPage = lazy(() => import('../features/auth/LoginPage'));
const DashboardPage = lazy(() => import('../features/dashboard/DashboardPage'));
const VehiclesPage = lazy(() => import('../features/vehicles/VehiclesPage'));
const DriversPage = lazy(() => import('../features/drivers/DriversPage'));
const TelemetryPage = lazy(() => import('../features/telemetry/TelemetryPage'));
const AlertsPage = lazy(() => import('../features/alerts/AlertsPage'));

// Admin-specific pages
const AdminDashboard = lazy(() => import('../features/admin/AdminDashboard'));
const UserManagement = lazy(() => import('../features/admin/UserManagement'));
const SystemSettings = lazy(() => import('../features/admin/SystemSettings'));
const ReportsPage = lazy(() => import('../features/reports/ReportsPage'));

// Manager-specific pages
const ManagerDashboard = lazy(() => import('../features/manager/ManagerDashboard'));
const FleetOverview = lazy(() => import('../features/manager/FleetOverview'));

// Driver-specific pages
const DriverDashboard = lazy(() => import('../features/driver/DriverDashboard'));
const MyVehicle = lazy(() => import('../features/driver/MyVehicle'));
const TripHistory = lazy(() => import('../features/driver/TripHistory'));

// Wrapper component for lazy loaded components
const LazyWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<LoadingSpinner />}>{children}</Suspense>
);

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to='/dashboard' replace />,
  },
  {
    path: '/auth',
    element: <AuthLayout />,
    children: [
      {
        path: 'login',
        element: (
          <LazyWrapper>
            <LoginPage />
          </LazyWrapper>
        ),
      },
      {
        index: true,
        element: <Navigate to='/auth/login' replace />,
      },
    ],
  },
  {
    path: '/',
    element: (
      <ProtectedRoute>
        <MainLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        path: 'dashboard',
        element: (
          <LazyWrapper>
            <DashboardPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'vehicles',
        element: (
          <LazyWrapper>
            <VehiclesPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'drivers',
        element: (
          <LazyWrapper>
            <DriversPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'telemetry',
        element: (
          <LazyWrapper>
            <TelemetryPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'alerts',
        element: (
          <LazyWrapper>
            <AlertsPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'reports',
        element: (
          <ProtectedRoute requiredPermissions={['view_reports']}>
            <LazyWrapper>
              <ReportsPage />
            </LazyWrapper>
          </ProtectedRoute>
        ),
      },
    ],
  },
  // Admin Routes
  {
    path: '/admin',
    element: (
      <ProtectedRoute requiredRole="admin">
        <MainLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: (
          <LazyWrapper>
            <AdminDashboard />
          </LazyWrapper>
        ),
      },
      {
        path: 'users',
        element: (
          <LazyWrapper>
            <UserManagement />
          </LazyWrapper>
        ),
      },
      {
        path: 'settings',
        element: (
          <LazyWrapper>
            <SystemSettings />
          </LazyWrapper>
        ),
      },
    ],
  },
  // Manager Routes
  {
    path: '/manager',
    element: (
      <ProtectedRoute allowedRoles={['admin', 'manager']}>
        <MainLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: (
          <LazyWrapper>
            <ManagerDashboard />
          </LazyWrapper>
        ),
      },
      {
        path: 'fleet',
        element: (
          <LazyWrapper>
            <FleetOverview />
          </LazyWrapper>
        ),
      },
    ],
  },
  // Driver Routes
  {
    path: '/driver',
    element: (
      <ProtectedRoute allowedRoles={['admin', 'manager', 'driver']}>
        <MainLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: (
          <LazyWrapper>
            <DriverDashboard />
          </LazyWrapper>
        ),
      },
      {
        path: 'my-vehicle',
        element: (
          <LazyWrapper>
            <MyVehicle />
          </LazyWrapper>
        ),
      },
      {
        path: 'trips',
        element: (
          <LazyWrapper>
            <TripHistory />
          </LazyWrapper>
        ),
      },
    ],
  },
  {
    path: '*',
    element: <Navigate to='/dashboard' replace />,
  },
]);

export default router;
